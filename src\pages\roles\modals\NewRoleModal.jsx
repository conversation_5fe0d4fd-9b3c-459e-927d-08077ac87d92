/* eslint-disable no-unused-vars */
import React, { useState, useCallback } from 'react';
import {
  Grid,
  <PERSON>po<PERSON>,
  TextField,
  Button,
  Alert,
  CircularProgress,
  FormControlLabel,
  Switch,
  Box,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  OutlinedInput
} from '@mui/material';
import ReusableModal from 'components/modal/reusable';
import { useCreateRole } from 'api/requests';
import { dispatch } from 'store';
import { openSnackbar } from 'store/reducers/snackbar';
import { validateRoleForm, formatPermissionName } from '../util';

const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250
    }
  }
};

const NewRoleModal = ({ open, onClose, formData, onFormChange, refetchRoles, permissions = [] }) => {
  const [error, setError] = useState('');
  const [validationErrors, setValidationErrors] = useState({});
  const createRoleMutation = useCreateRole();

  const handleInputChange = useCallback(
    (field, value) => {
      onFormChange('newRole', field, value);
      // Clear validation error for this field
      if (validationErrors[field]) {
        setValidationErrors((prev) => ({ ...prev, [field]: '' }));
      }
    },
    [onFormChange, validationErrors]
  );

  const handlePermissionChange = (event) => {
    const value = event.target.value;
    const selectedPermissions = typeof value === 'string' ? value.split(',') : value;
    handleInputChange('permissions', selectedPermissions);
  };

  const handleSubmit = async () => {
    try {
      setError('');
      setValidationErrors({});

      // Validate form
      const errors = validateRoleForm(formData);
      if (Object.keys(errors).length > 0) {
        setValidationErrors(errors);
        return;
      }

      // Prepare data for submission
      const submitData = {
        name: formData.name.trim(),
        description: formData.description?.trim() || '',
        permissions: formData.permissions || [],
        active: formData.active
      };

      await createRoleMutation.mutateAsync(submitData);

      dispatch(
        openSnackbar({
          open: true,
          message: 'Role created successfully!',
          variant: 'alert',
          alert: {
            color: 'success'
          }
        })
      );

      refetchRoles();
      onClose();
    } catch (error) {
      console.error('Error creating role:', error);
      setError(error.response?.data?.message || 'Failed to create role. Please try again.');
    }
  };

  const actions = (
    <>
      <Button onClick={onClose} variant="outlined" disabled={createRoleMutation.isPending}>
        Cancel
      </Button>
      <Button
        onClick={handleSubmit}
        variant="contained"
        disabled={createRoleMutation.isPending}
        startIcon={createRoleMutation.isPending && <CircularProgress size={20} />}
      >
        {createRoleMutation.isPending ? 'Creating...' : 'Create Role'}
      </Button>
    </>
  );

  return (
    <ReusableModal
      open={open}
      onClose={onClose}
      title="Create New Role"
      description="Create a new role and assign permissions to it."
      actions={actions}
      maxWidth={600}
    >
      <Grid container spacing={3}>
        {error && (
          <Grid item xs={12}>
            <Alert severity="error">{error}</Alert>
          </Grid>
        )}

        <Grid item xs={12}>
          <TextField
            fullWidth
            label="Role Name"
            placeholder="Enter role name"
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            error={!!validationErrors.name}
            helperText={validationErrors.name}
            required
          />
        </Grid>

        <Grid item xs={12}>
          <TextField
            fullWidth
            label="Description"
            placeholder="Enter role description (optional)"
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            error={!!validationErrors.description}
            helperText={validationErrors.description}
            multiline
            rows={3}
          />
        </Grid>

        <Grid item xs={12}>
          <FormControl fullWidth>
            <InputLabel>Permissions</InputLabel>
            <Select
              multiple
              value={formData.permissions || []}
              onChange={handlePermissionChange}
              input={<OutlinedInput label="Permissions" />}
              renderValue={(selected) => (
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                  {selected.map((value) => {
                    const permission = permissions.find((p) => p.id === value);
                    return <Chip key={value} label={permission ? formatPermissionName(permission.name) : value} size="small" />;
                  })}
                </Box>
              )}
              MenuProps={MenuProps}
            >
              {permissions.map((permission) => (
                <MenuItem key={permission.id} value={permission.id}>
                  <Box>
                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                      {formatPermissionName(permission.name)}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {permission.resource} - {permission.action}
                    </Typography>
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12}>
          <FormControlLabel
            control={<Switch checked={formData.active} onChange={(e) => handleInputChange('active', e.target.checked)} color="primary" />}
            label="Active"
          />
          <Typography variant="caption" color="text.secondary" display="block">
            Inactive roles cannot be assigned to users
          </Typography>
        </Grid>
      </Grid>
    </ReusableModal>
  );
};

export default NewRoleModal;
