/* eslint-disable no-unused-vars */
import '../../assets/datestyle.css';

import React, { useMemo } from 'react';
import { ThemeProvider } from '@mui/material/styles';
import { Box, Container } from '@mui/material';

// Project imports
import { theme } from './util';
import { useRoleState } from './hooks/useRoleState';
import { useRoleActions } from './hooks/useRoleActions';
import RoleStats from './components/RoleStats';
import RoleFilters from './components/RoleFilters';
import RoleTable from './components/RoleTable';
import PermissionTable from './components/PermissionTable';
import RoleModals from './modals/RoleModals';
import PermissionModals from './modals/PermissionModals';
import { useGetRoles, useGetPermissions } from 'api/requests';

export default function RolesAndPermissions() {
  // Use consolidated state management
  const { state, updateState, updateNestedState, toggleModal, updateForm, resetForm } = useRoleState();

  // Fetch roles data
  const {
    data: roles,
    isLoading: rolesLoading,
    refetch: refetchRoles
  } = useGetRoles({
    page: state.page,
    size: state.rowsPerPage,
    search: state.searchTerm || state.tableSearchTerm || '',
    sort: ['desc']
  });

  // Fetch permissions data
  const {
    data: permissions,
    isLoading: permissionsLoading,
    refetch: refetchPermissions
  } = useGetPermissions({
    page: state.permissionPage,
    size: state.permissionRowsPerPage,
    search: state.permissionSearchTerm || state.permissionTableSearchTerm || '',
    sort: ['desc']
  });
  console.log('🚀 ~ RolesAndPermissions ~ permissions:', permissions);

  const roleData = roles?.content;
  const permissionData = permissions?.data.content;
  // Use actions hook
  const actions = useRoleActions(state, updateState, toggleModal, resetForm, roles?.content);

  // Handle form changes
  const handleFormChange = (formName, field, value) => {
    updateForm(formName, { [field]: value });
  };

  return (
    <ThemeProvider theme={theme}>
      <Box
        sx={{
          minHeight: '100vh',
          backgroundColor: 'background.default',
          py: 3
        }}
      >
        <Container maxWidth="xl">
          {/* Stats Component */}
          <RoleStats
            rolesCount={roles?.totalElements || 0}
            permissionsCount={permissions?.data?.totalElements || 0}
            activeRolesCount={roleData?.filter((role) => role.active)?.length || 0}
          />

          {/* Filters Component */}
          <RoleFilters
            state={state}
            onSearchChange={actions.handleSearchChange}
            onStatusChange={actions.handleStatusChange}
            onToggleFilters={actions.toggleFilters}
          />

          {/* Table Components */}
          {state.tabValue === 0 ? (
            <RoleTable
              state={state}
              rolesData={roles}
              isLoading={rolesLoading}
              onTabChange={actions.handleTabChange}
              onPageChange={actions.handleChangePage}
              onRowsPerPageChange={actions.handleChangeRowsPerPage}
              onTableSearchChange={actions.handleTableSearchChange}
              onOpenNewRole={actions.handleOpenNewRole}
              onEditRole={actions.handleOpenEdit}
              onDeleteRole={actions.handleOpenDelete}
              onViewRole={actions.handleOpenView}
            />
          ) : (
            <PermissionTable
              state={state}
              permissionsData={permissions?.data || []}
              isLoading={permissionsLoading}
              onTabChange={actions.handleTabChange}
              onPageChange={actions.handlePermissionPageChange}
              onRowsPerPageChange={actions.handlePermissionRowsPerPageChange}
              onTableSearchChange={actions.handlePermissionTableSearchChange}
              onOpenNewPermission={actions.handleOpenNewPermission}
              onEditPermission={actions.handleOpenEditPermission}
              onDeletePermission={actions.handleOpenDeletePermission}
              onViewPermission={actions.handleOpenViewPermission}
            />
          )}

          {/* Modals Components */}
          <RoleModals
            state={state}
            actions={actions}
            onFormChange={handleFormChange}
            refetchRoles={refetchRoles}
            permissions={permissions?.data?.content}
          />

          <PermissionModals state={state} actions={actions} onFormChange={handleFormChange} refetchPermissions={refetchPermissions} />
        </Container>
      </Box>
    </ThemeProvider>
  );
}
