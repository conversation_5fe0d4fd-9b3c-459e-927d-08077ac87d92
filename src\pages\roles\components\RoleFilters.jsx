/* eslint-disable no-unused-vars */

import React from 'react';
import {
  Card,
  CardContent,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  Box,
  Typography,
  Collapse,
  useTheme
} from '@mui/material';
import { SearchNormal1, Filter, ArrowDown2, ArrowUp2 } from 'iconsax-react';
import { ROLE_STATUS_OPTIONS, PERMISSION_STATUS_OPTIONS } from '../util';

const RoleFilters = ({ state, onSearchChange, onStatusChange, onToggleFilters }) => {
  const theme = useTheme();

  return (
    <Card sx={{ mb: 3, overflow: 'visible' }}>
      <CardContent sx={{ pb: state.filtersExpanded ? 3 : 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
          <Typography variant="h6" sx={{ fontWeight: 600, color: theme.palette.text.primary }}>
            Search & Filters
          </Typography>
          <IconButton onClick={onToggleFilters} size="small">
            {state.filtersExpanded ? <ArrowUp2 /> : <ArrowDown2 />}
          </IconButton>
        </Box>

        <Collapse in={state.filtersExpanded}>
          <Grid container spacing={3}>
            {/* Search Field */}
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Search"
                placeholder={
                  state.tabValue === 0 ? 'Search roles by name or description...' : 'Search permissions by name, resource, or action...'
                }
                value={state.tabValue === 0 ? state.searchTerm : state.permissionSearchTerm}
                onChange={state.tabValue === 0 ? onSearchChange : onSearchChange}
                InputProps={{
                  startAdornment: <SearchNormal1 size="20" color={theme.palette.text.secondary} style={{ marginRight: 8 }} />
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: theme.palette.primary.main
                    }
                  }
                }}
              />
            </Grid>

            {/* Status Filter */}
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  value={state.tabValue === 0 ? state.status : state.permissionStatus}
                  onChange={onStatusChange}
                  label="Status"
                  sx={{
                    borderRadius: 2,
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: theme.palette.primary.main
                    }
                  }}
                >
                  {(state.tabValue === 0 ? ROLE_STATUS_OPTIONS : PERMISSION_STATUS_OPTIONS).map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </Collapse>
      </CardContent>
    </Card>
  );
};

export default RoleFilters;
